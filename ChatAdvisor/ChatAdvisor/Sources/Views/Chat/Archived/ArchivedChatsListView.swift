//
//  ArchivedChatsListView.swift
//  JunShi
//
//  Created by md on 2024/5/6.
//

import Foundation
import SwifterSwift
import SwiftUI

struct ArchivedChatsListView: View {
    @FocusState private var isTextFieldFocused: Bool
    @ObservedObject var viewModel: ArchivedChatViewModel

    var body: some View {
        VStack {
            // {{ AURA-X: Modify - 统一搜索框样式，与ChatsListView保持一致 }}
            HStack {
                TextField("搜索聊天".localized(), text: $viewModel.searchText)
                    .padding(AppThemes.padding)
                    .background(Color(UIColor(light: .systemGray4, dark: .systemGray2)))
                    .cornerRadius(8)
                    .padding(.horizontal)
                    .onChange(of: viewModel.searchText) { newValue in
                        if newValue.isEmpty {
                            viewModel.refreshChats()
                        } else {
                            viewModel.searchChats()
                        }
                    }
                    .focused($isTextFieldFocused)
                    .onChange(of: isTextFieldFocused) { isFocused in
                        if isFocused == false {
                            viewModel.refreshChats()
                        }
                    }
            }
            // {{ AURA-X: Modify - 使用与ChatsListView一致的现代化列表设计 }}
            ScrollView {
                LazyVStack(spacing: 8) {
                    // 使用 forEach 遍历分组
                    ForEach(Array(viewModel.groupedChats.keys.sorted(by: >)), id: \.self) { date in
                        Section {
                            ForEach(viewModel.groupedChats[date] ?? [], id: \.id) { chat in
                                ArchivedChatRowView(
                                    chat: chat,
                                    isSelected: chat.id == viewModel.currentChat.id,
                                    onTap: {
                                        viewModel.setCurrentChat(chat: chat)
                                    },
                                    onRename: {
                                        viewModel.isRenaming = true
                                        viewModel.newChatTitle = viewModel.getChatTitle(id: chat.id)
                                        viewModel.renamingChatId = chat.id
                                    },
                                    onUnarchive: {
                                        viewModel.unarchiveChat(id: chat.id)
                                    },
                                    onDelete: {
                                        viewModel.deleteChat(id: chat.id)
                                    }
                                )
                                .id(chat.id)
                                .padding(.horizontal, 8)
                            }
                            .listRowSeparator(.hidden)
                        } header: {
                            // {{ AURA-X: Modify - 使用与ChatsListView一致的分组头部样式 }}
                            ArchivedGroupHeaderView(date: date)
                        }
                    }
                    .ignoresSafeArea()
                    if !viewModel.isLoadFinished {
                        ProgressView()
                            .tint(.accentColor)
                            .frame(maxWidth: .infinity, maxHeight: .infinity)
                            .onAppear {
                                viewModel.fetchMoreChat()
                            }
                    }
                }
            }
            .onAppear {
                viewModel.fetchMoreChat()
            }
        }
        .alert("重命名".localized(), isPresented: $viewModel.isRenaming) {
            TextField(viewModel.newChatTitle, text: $viewModel.newChatTitle)
            Button("取消".localized(), role: .cancel) {
                viewModel.isRenaming = false
            }
            Button("确认".localized()) {
                viewModel.renameChat()
            }
        } message: {
            Text("请输入新的聊天标题".localized())
        }
    }
}

// MARK: - ArchivedChatRowView

/// {{ AURA-X: Add - 归档聊天行视图组件，与OptimizedChatRowView保持一致的现代化设计 }}
struct ArchivedChatRowView: View {
    let chat: Chat
    let isSelected: Bool
    let onTap: () -> Void
    let onRename: () -> Void
    let onUnarchive: () -> Void
    let onDelete: () -> Void

    // UI状态管理
    @State private var isHovering = false
    @State private var isPressed = false

    // 缓存消息预览，避免重复计算
    @State private var cachedMessagePreview: String?
    @State private var lastChatMessagesCount: Int = 0

    // 缓存计算结果以提高性能
    private var displayTitle: String {
        chat.title.isEmpty ? "没有标题的会话".localized() : chat.title
    }

    private var lastMessagePreview: String? {
        let currentMessagesCount = chat.messages.count

        if cachedMessagePreview == nil || lastChatMessagesCount != currentMessagesCount {
            let preview = chat.messages.last(where: { $0.role == .user || $0.role == .assistant })?.content
            DispatchQueue.main.async {
                cachedMessagePreview = preview
                lastChatMessagesCount = currentMessagesCount
            }
            return preview
        }

        return cachedMessagePreview
    }

    // 动态颜色计算
    private var backgroundColor: Color {
        if isSelected {
            return Color.accentColor.opacity(0.85)
        } else if isHovering {
            return Color.accentColor.opacity(0.08)
        } else {
            return Color.clear
        }
    }

    private var shadowColor: Color {
        isSelected ? Color.accentColor.opacity(0.3) : Color.clear
    }

    var body: some View {
        Button(action: onTap) {
            HStack(spacing: 14) {
                // 固定内容区域宽度，避免选中图标影响布局
                VStack(alignment: .leading, spacing: 6) {
                    HStack {
                        Text(displayTitle)
                            .font(.system(.body, design: .rounded, weight: isSelected ? .semibold : .medium))
                            .foregroundColor(isSelected ? .white : .primary)
                            .frame(maxWidth: .infinity, alignment: .leading)
                            .lineLimit(1)

                        // 归档标识
                        Image(systemName: "archivebox.fill")
                            .font(.caption)
                            .foregroundColor(isSelected ? .white.opacity(0.7) : .secondary)
                    }

                    // 优化消息预览样式，固定宽度避免闪动
                    if let preview = lastMessagePreview {
                        Text(preview)
                            .font(.system(.caption, design: .rounded))
                            .foregroundColor(isSelected ? .white.opacity(0.85) : .secondary)
                            .lineLimit(2)
                            .multilineTextAlignment(.leading)
                    }
                }

                Spacer()

                // 选中状态指示器
                if isSelected {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.system(size: 18, weight: .medium))
                        .foregroundColor(.white)
                        .transition(.scale.combined(with: .opacity))
                }
            }
        }
        .buttonStyle(PlainButtonStyle())
        .contentShape(Rectangle())
        .padding(.vertical, 10)
        .padding(.horizontal, 14)
        .frame(minHeight: 68)
        .background(
            // 现代化背景设计
            RoundedRectangle(cornerRadius: 12)
                .fill(backgroundColor)
                .shadow(
                    color: shadowColor,
                    radius: isSelected ? 8 : 0,
                    x: 0,
                    y: isSelected ? 2 : 0
                )
        )
        .overlay(
            // 精致的边框效果
            RoundedRectangle(cornerRadius: 12)
                .stroke(
                    LinearGradient(
                        colors: isSelected ? [
                            Color.white.opacity(0.3),
                            Color.white.opacity(0.1)
                        ] : [Color.clear],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    ),
                    lineWidth: isSelected ? 1 : 0
                )
        )
        .scaleEffect(isPressed ? 0.97 : 1.0)
        .contextMenu {
            contextMenuContent
        }
        // 优化动画机制，减少冲突和闪动
        .animation(.spring(response: 0.3, dampingFraction: 0.9, blendDuration: 0), value: isSelected)
        .animation(.easeInOut(duration: 0.12), value: isHovering)
        .animation(.easeInOut(duration: 0.08), value: isPressed)
        // 交互手势
        .onTapGesture {
            withAnimation(.easeInOut(duration: 0.1)) {
                isPressed = true
            }
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                withAnimation(.easeInOut(duration: 0.1)) {
                    isPressed = false
                }
            }
            onTap()
        }
        .onHover { hovering in
            withAnimation(.easeInOut(duration: 0.2)) {
                isHovering = hovering
            }
        }
    }

    @ViewBuilder
    private var contextMenuContent: some View {
        Button(action: onRename) {
            Label("重命名".localized(), systemImage: "pencil")
        }

        Button(action: onUnarchive) {
            Label("取消归档".localized(), systemImage: "archivebox")
        }

        Divider()

        Button(action: onDelete) {
            Label("删除".localized(), systemImage: "trash")
        }
        .foregroundColor(.red)
    }
}

// MARK: - ArchivedGroupHeaderView

/// {{ AURA-X: Add - 归档聊天分组头部视图，与GroupHeaderView保持一致的现代化设计 }}
struct ArchivedGroupHeaderView: View {
    let date: Date

    var body: some View {
        HStack {
            // 现代化分组标题设计
            Text(date.chatDateLabel())
                .font(.system(.subheadline, design: .rounded, weight: .semibold))
                .foregroundColor(.primary.opacity(0.8))

            Spacer()

            // 装饰性分割线
            Rectangle()
                .fill(Color.secondary.opacity(0.2))
                .frame(height: 1)
                .frame(maxWidth: .infinity)
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(
            // 毛玻璃效果背景
            Rectangle()
                .fill(.ultraThinMaterial)
                .opacity(0.8)
        )
    }
}
