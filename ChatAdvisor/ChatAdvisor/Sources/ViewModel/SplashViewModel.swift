//
//  SplashViewModel.swift
//  ChatAdvisor
//
//  Created by AI Assistant on 2025/01/02.
//

import Foundation
import SwiftUI
import UIKit
import OSLog

private let logger = Logger(subsystem: "com.sanva.chatadvisor", category: "SplashViewModel")

/// 开屏初始化视图模型
@MainActor
class SplashViewModel: ObservableObject {
    @Published var progress: Double = 0.0
    @Published var statusText: String = "正在启动..."
    @Published var logoScale: CGFloat = 1.0
    @Published var textOpacity: Double = 0.0

    // {{ AURA-X: Add - 添加强制升级相关状态管理. Approval: mcp-feedback-enhanced(ID:20250129011). }}
    @Published var showForceUpdate = false
    @Published var forceUpdateInfo: VersionControl?

    private var initializationSteps: [InitializationStep] = []
    private var currentStepIndex = 0
    
    init() {
        setupInitializationSteps()
        startAnimations()
    }
    
    /// 设置初始化步骤
    private func setupInitializationSteps() {
        initializationSteps = [
            InitializationStep(
                name: "初始化数据库连接",
                action: { try? await self.initializeDatabase() }
            ),
            InitializationStep(
                name: "加载用户配置",
                action: { try? await self.loadUserConfiguration() }
            ),
            // {{ AURA-X: Add - 添加节点检测步骤，在获取服务器配置之前进行. Approval: mcp-feedback-enhanced(ID:20250129011). }}
            InitializationStep(
                name: "检测最优节点",
                action: { await self.performNodeDetection() }
            ),
            // {{ AURA-X: Add - 添加版本检测步骤，在节点检测之后进行. Approval: mcp-feedback-enhanced(ID:20250129011). }}
            InitializationStep(
                name: "检查版本更新",
                action: { await self.performVersionCheck() }
            ),
            InitializationStep(
                name: "获取服务器配置",
                action: { await self.loadServerConfiguration() }
            ),
            InitializationStep(
                name: "预加载会话数据",
                action: { try? await self.preloadSessionData() }
            ),
            InitializationStep(
                name: "准备用户界面",
                action: { try? await self.prepareUserInterface() }
            ),
            InitializationStep(
                name: "完成初始化",
                action: { try? await self.finalizeInitialization() }
            )
        ]
    }
    
    /// 启动动画
    private func startAnimations() {
        logoScale = 1.2
        textOpacity = 1.0
    }
    
    /// 开始初始化过程
    func startInitialization(completion: @escaping (Bool) -> Void) {
        Task {
            var success = true

            for (index, step) in initializationSteps.enumerated() {
                currentStepIndex = index

                // 更新状态文本
                statusText = step.name

                // 更新进度
                let newProgress = Double(index) / Double(initializationSteps.count)
                withAnimation(.easeInOut(duration: 0.3)) {
                    progress = newProgress
                }

                do {
                    // 执行初始化步骤
                    try await step.action()

                    // {{ AURA-X: Modify - 移除sleep延时，按用户要求不使用延时机制. Approval: mcp-feedback-enhanced(ID:20250129004). }}
                    // 直接继续下一步，不使用延时

                } catch {
                    logger.error("初始化步骤失败: \(step.name), 错误: \(error)")
                    // {{ AURA-X: Modify - 即使某个步骤失败也继续启动流程，避免卡住. Approval: mcp-feedback-enhanced(ID:20250129004). }}
                    // 记录错误但不中断启动流程，确保用户能够进入应用
                    logger.warning("步骤失败但继续启动流程: \(step.name)")
                    // 继续执行，不设置success=false，不break
                }

                // {{ AURA-X: Add - 检查是否需要强制升级，如果需要则暂停初始化. Approval: mcp-feedback-enhanced(ID:20250129011). }}
                if showForceUpdate {
                    logger.info("检测到强制升级需求，暂停初始化流程")
                    // 不调用completion，保持在闪屏页状态
                    return
                }
            }

            // 完成进度
            withAnimation(.easeInOut(duration: 0.5)) {
                progress = 1.0
                statusText = success ? "初始化完成" : "初始化失败"
            }

            completion(success)
        }
    }
    
    // MARK: - 初始化步骤实现
    
    /// 初始化数据库连接
    private func initializeDatabase() async throws {
        // 确保数据库管理器已初始化
        _ = AdvisorDatabaseManager.shared.database
        
        // 等待数据库准备完成
        let isDatabaseReady = await AdvisorDatabaseManager.shared.waitForDatabaseReady()
        guard isDatabaseReady else {
            throw InitializationError.databaseInitializationFailed
        }
        
        logger.info("数据库初始化完成")
    }
    
    /// 加载用户配置
    private func loadUserConfiguration() async throws {
        // 预加载用户偏好设置
        _ = Preferences.hasLaunchedBefore.value

        // 如果用户已登录，预加载账户信息
        if AccountManager.shared.isLoggedIn {
            // 这里可以预加载一些用户相关的配置
        }

        logger.info("用户配置加载完成")
    }

    /// 加载服务器配置
    private func loadServerConfiguration() async {
        // {{ AURA-X: Modify - 简化配置加载，确保不会抛出异常阻塞启动. Approval: mcp-feedback-enhanced(ID:***********). }}
        // AppDelegate已经负责启动时的API调用，这里只做非阻塞检查
        // 即使getConfig失败，也不影响启动流程
        
        // 检查是否正在加载中，避免重复触发
        if !BootManager.shared.isLoading {
            // 如果没有在加载，触发加载
            BootManager.shared.getConfig()
        }
        
        logger.info("服务器配置检查完成（非阻塞）")
    }
    
    /// 预加载会话数据
    private func preloadSessionData() async throws {
        // {{ AURA-X: Modify - 预加载会话数据并设置初始状态，避免界面闪烁. Approval: mcp-feedback-enhanced(ID:20250129009). }}
        // 预查询最后一个聊天ID
        let lastChatId = await AdvisorDatabaseManager.shared.getLastChatId()
        
        if let lastChatId = lastChatId {
            // 预加载最后一个会话的数据
            let chat = await AdvisorDatabaseManager.shared.fetchChat(id: lastChatId)
            
            // 提前设置会话状态，避免界面闪烁
            await ChatSessionManager.shared.setLastChatIdForPreload(lastChatId)
            
            logger.info("预加载会话数据完成: \(lastChatId)")
        } else {
            // 标记没有历史会话
            await ChatSessionManager.shared.setNoHistoryDataFlag()
            logger.info("没有找到历史会话，已标记空状态")
        }
    }
    
    /// 准备用户界面
    private func prepareUserInterface() async throws {
        // 预加载一些UI相关的资源
        // 这里可以预加载图片、字体等资源
        
        // 性能监控已移除
        
        logger.info("用户界面准备完成")
    }
    
    /// 完成初始化
    private func finalizeInitialization() async throws {
        // {{ AURA-X: Modify - 同步标记AppStartupManager完成，统一双重启动系统. Approval: mcp-feedback-enhanced(ID:20250129006). }}
        // 最后的清理和验证工作
        await AppStartupManager.shared.markStartupComplete()
        logger.info("所有初始化步骤完成，已同步AppStartupManager状态")
    }

    // MARK: - 新增的初始化步骤实现

    /// 执行节点检测
    private func performNodeDetection() async {
        logger.info("开始执行节点检测...")

        do {
            // 调用RegionDetector进行智能节点选择
            let result = await RegionDetector.shared.detectOptimalEndpoint(forceRefresh: true)

            // 更新BootManager的节点选择结果
            await MainActor.run {
                BootManager.shared.isChina = result.isChina
            }

            logger.info("节点检测完成: \(result.selectedEndpoint), 置信度: \(result.confidence)")

        } catch {
            logger.error("节点检测失败: \(error.localizedDescription)")
            // 节点检测失败不影响应用启动，使用默认配置
        }
    }

    /// 执行版本检测
    private func performVersionCheck() async {
        logger.info("开始执行版本检测...")

        do {
            // 使用专门的闪屏页版本检测方法
            let versionResult = await checkVersionForSplash()

            if let versionControl = versionResult,
               versionControl.needUpdate && versionControl.updateType.isForceUpdate {
                // 检测到强制升级
                await MainActor.run {
                    self.forceUpdateInfo = versionControl
                    self.showForceUpdate = true
                }
                logger.warning("检测到强制升级需求: \(versionControl.latestVersion)")
            } else {
                logger.info("版本检测完成，无需强制升级")
            }

        } catch {
            logger.error("版本检测失败: \(error.localizedDescription)")
            // 版本检测失败不影响应用启动
        }
    }

    /// 专门用于闪屏页的版本检测方法
    private func checkVersionForSplash() async -> VersionControl? {
        return await withCheckedContinuation { continuation in
            // 获取当前应用版本
            let currentVersion = Bundle.main.object(forInfoDictionaryKey: "CFBundleShortVersionString") as? String ?? "1.0.0"

            // 调用网络服务检查版本
            NetworkService.shared.requestMulti(VersionCheckTarget.checkVersion) { (result: Result<NetworkResponse<VersionCheckResponse>, NetworkError>) in
                switch result {
                case .success(let response):
                    if response.isSuccess, let versionData = response.data {
                        // 确保downloadUrl不为空
                        let downloadUrl = versionData.downloadUrl.isEmpty ?
                            "https://apps.apple.com/us/app/chat-advisor/id6526465428" :
                            versionData.downloadUrl

                        let versionControl = VersionControl(
                            needUpdate: versionData.needUpdate,
                            updateType: VersionControl.UpdateType(rawValue: versionData.updateType) ?? .none,
                            latestVersion: versionData.latestVersion,
                            minimumVersion: versionData.minimumVersion,
                            updateMessage: versionData.updateMessage,
                            downloadUrl: downloadUrl,
                            versionCheckEnabled: true
                        )

                        continuation.resume(returning: versionControl)
                    } else {
                        continuation.resume(returning: nil)
                    }

                case .failure(let error):
                    logger.error("版本检测网络请求失败: \(error.localizedDescription)")
                    continuation.resume(returning: nil)
                }
            }
        }
    }

    /// 处理强制升级按钮点击
    func handleForceUpdateTap() {
        guard let updateInfo = forceUpdateInfo else { return }

        if let url = URL(string: updateInfo.downloadUrl) {
            UIApplication.shared.open(url)
        }

        // 弹窗保持显示，不自动消失
    }
}

// MARK: - 支持类型

/// 初始化步骤
struct InitializationStep {
    let name: String
    let action: () async throws -> Void
}

/// 初始化错误
enum InitializationError: LocalizedError {
    case databaseInitializationFailed
    case userConfigurationLoadFailed
    case sessionDataPreloadFailed
    case userInterfacePreparationFailed
    
    var errorDescription: String? {
        switch self {
        case .databaseInitializationFailed:
            return "数据库初始化失败"
        case .userConfigurationLoadFailed:
            return "用户配置加载失败"
        case .sessionDataPreloadFailed:
            return "会话数据预加载失败"
        case .userInterfacePreparationFailed:
            return "用户界面准备失败"
        }
    }
}
